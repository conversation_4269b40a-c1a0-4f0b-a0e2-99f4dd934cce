"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DashboardPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_layout_main_layout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/main-layout */ \"(app-pages-browser)/./components/layout/main-layout.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_progress__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/progress */ \"(app-pages-browser)/./components/ui/progress.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./components/ui/alert.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./components/ui/separator.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bell_BookOpen_Calendar_Clock_Cloud_CloudRain_FileText_MapPin_MessageSquare_QrCode_RefreshCw_Search_Sun_TrendingUp_UserCheck_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bell,BookOpen,Calendar,Clock,Cloud,CloudRain,FileText,MapPin,MessageSquare,QrCode,RefreshCw,Search,Sun,TrendingUp,UserCheck,UserX,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sun.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bell_BookOpen_Calendar_Clock_Cloud_CloudRain_FileText_MapPin_MessageSquare_QrCode_RefreshCw_Search_Sun_TrendingUp_UserCheck_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bell,BookOpen,Calendar,Clock,Cloud,CloudRain,FileText,MapPin,MessageSquare,QrCode,RefreshCw,Search,Sun,TrendingUp,UserCheck,UserX,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/cloud.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bell_BookOpen_Calendar_Clock_Cloud_CloudRain_FileText_MapPin_MessageSquare_QrCode_RefreshCw_Search_Sun_TrendingUp_UserCheck_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bell,BookOpen,Calendar,Clock,Cloud,CloudRain,FileText,MapPin,MessageSquare,QrCode,RefreshCw,Search,Sun,TrendingUp,UserCheck,UserX,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/cloud-rain.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bell_BookOpen_Calendar_Clock_Cloud_CloudRain_FileText_MapPin_MessageSquare_QrCode_RefreshCw_Search_Sun_TrendingUp_UserCheck_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bell,BookOpen,Calendar,Clock,Cloud,CloudRain,FileText,MapPin,MessageSquare,QrCode,RefreshCw,Search,Sun,TrendingUp,UserCheck,UserX,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bell_BookOpen_Calendar_Clock_Cloud_CloudRain_FileText_MapPin_MessageSquare_QrCode_RefreshCw_Search_Sun_TrendingUp_UserCheck_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bell,BookOpen,Calendar,Clock,Cloud,CloudRain,FileText,MapPin,MessageSquare,QrCode,RefreshCw,Search,Sun,TrendingUp,UserCheck,UserX,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bell_BookOpen_Calendar_Clock_Cloud_CloudRain_FileText_MapPin_MessageSquare_QrCode_RefreshCw_Search_Sun_TrendingUp_UserCheck_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bell,BookOpen,Calendar,Clock,Cloud,CloudRain,FileText,MapPin,MessageSquare,QrCode,RefreshCw,Search,Sun,TrendingUp,UserCheck,UserX,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bell_BookOpen_Calendar_Clock_Cloud_CloudRain_FileText_MapPin_MessageSquare_QrCode_RefreshCw_Search_Sun_TrendingUp_UserCheck_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bell,BookOpen,Calendar,Clock,Cloud,CloudRain,FileText,MapPin,MessageSquare,QrCode,RefreshCw,Search,Sun,TrendingUp,UserCheck,UserX,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-check.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bell_BookOpen_Calendar_Clock_Cloud_CloudRain_FileText_MapPin_MessageSquare_QrCode_RefreshCw_Search_Sun_TrendingUp_UserCheck_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bell,BookOpen,Calendar,Clock,Cloud,CloudRain,FileText,MapPin,MessageSquare,QrCode,RefreshCw,Search,Sun,TrendingUp,UserCheck,UserX,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-x.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bell_BookOpen_Calendar_Clock_Cloud_CloudRain_FileText_MapPin_MessageSquare_QrCode_RefreshCw_Search_Sun_TrendingUp_UserCheck_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bell,BookOpen,Calendar,Clock,Cloud,CloudRain,FileText,MapPin,MessageSquare,QrCode,RefreshCw,Search,Sun,TrendingUp,UserCheck,UserX,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bell_BookOpen_Calendar_Clock_Cloud_CloudRain_FileText_MapPin_MessageSquare_QrCode_RefreshCw_Search_Sun_TrendingUp_UserCheck_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bell,BookOpen,Calendar,Clock,Cloud,CloudRain,FileText,MapPin,MessageSquare,QrCode,RefreshCw,Search,Sun,TrendingUp,UserCheck,UserX,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bell_BookOpen_Calendar_Clock_Cloud_CloudRain_FileText_MapPin_MessageSquare_QrCode_RefreshCw_Search_Sun_TrendingUp_UserCheck_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bell,BookOpen,Calendar,Clock,Cloud,CloudRain,FileText,MapPin,MessageSquare,QrCode,RefreshCw,Search,Sun,TrendingUp,UserCheck,UserX,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/qr-code.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bell_BookOpen_Calendar_Clock_Cloud_CloudRain_FileText_MapPin_MessageSquare_QrCode_RefreshCw_Search_Sun_TrendingUp_UserCheck_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bell,BookOpen,Calendar,Clock,Cloud,CloudRain,FileText,MapPin,MessageSquare,QrCode,RefreshCw,Search,Sun,TrendingUp,UserCheck,UserX,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bell_BookOpen_Calendar_Clock_Cloud_CloudRain_FileText_MapPin_MessageSquare_QrCode_RefreshCw_Search_Sun_TrendingUp_UserCheck_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bell,BookOpen,Calendar,Clock,Cloud,CloudRain,FileText,MapPin,MessageSquare,QrCode,RefreshCw,Search,Sun,TrendingUp,UserCheck,UserX,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bell_BookOpen_Calendar_Clock_Cloud_CloudRain_FileText_MapPin_MessageSquare_QrCode_RefreshCw_Search_Sun_TrendingUp_UserCheck_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bell,BookOpen,Calendar,Clock,Cloud,CloudRain,FileText,MapPin,MessageSquare,QrCode,RefreshCw,Search,Sun,TrendingUp,UserCheck,UserX,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bell_BookOpen_Calendar_Clock_Cloud_CloudRain_FileText_MapPin_MessageSquare_QrCode_RefreshCw_Search_Sun_TrendingUp_UserCheck_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bell,BookOpen,Calendar,Clock,Cloud,CloudRain,FileText,MapPin,MessageSquare,QrCode,RefreshCw,Search,Sun,TrendingUp,UserCheck,UserX,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bell_BookOpen_Calendar_Clock_Cloud_CloudRain_FileText_MapPin_MessageSquare_QrCode_RefreshCw_Search_Sun_TrendingUp_UserCheck_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bell,BookOpen,Calendar,Clock,Cloud,CloudRain,FileText,MapPin,MessageSquare,QrCode,RefreshCw,Search,Sun,TrendingUp,UserCheck,UserX,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bell_BookOpen_Calendar_Clock_Cloud_CloudRain_FileText_MapPin_MessageSquare_QrCode_RefreshCw_Search_Sun_TrendingUp_UserCheck_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bell,BookOpen,Calendar,Clock,Cloud,CloudRain,FileText,MapPin,MessageSquare,QrCode,RefreshCw,Search,Sun,TrendingUp,UserCheck,UserX,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bell_BookOpen_Calendar_Clock_Cloud_CloudRain_FileText_MapPin_MessageSquare_QrCode_RefreshCw_Search_Sun_TrendingUp_UserCheck_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bell,BookOpen,Calendar,Clock,Cloud,CloudRain,FileText,MapPin,MessageSquare,QrCode,RefreshCw,Search,Sun,TrendingUp,UserCheck,UserX,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bell_BookOpen_Calendar_Clock_Cloud_CloudRain_FileText_MapPin_MessageSquare_QrCode_RefreshCw_Search_Sun_TrendingUp_UserCheck_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bell,BookOpen,Calendar,Clock,Cloud,CloudRain,FileText,MapPin,MessageSquare,QrCode,RefreshCw,Search,Sun,TrendingUp,UserCheck,UserX,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/component/ResponsiveContainer.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/chart/LineChart.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/CartesianGrid.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/XAxis.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/YAxis.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/component/Tooltip.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/Line.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n// Mock data for the dashboard\nconst mockStats = {\n    totalStudents: 1234,\n    presentToday: 1089,\n    absentToday: 145,\n    lateToday: 23,\n    attendanceRate: 88.2,\n    totalCourses: 12,\n    activeSections: 45\n};\nconst mockWeather = {\n    temperature: 28,\n    condition: \"Partly Cloudy\",\n    humidity: 65,\n    icon: \"partly-cloudy\"\n};\nconst mockRecentActivity = [\n    {\n        id: \"1\",\n        studentName: \"Juan Dela Cruz\",\n        studentId: \"2021-001234\",\n        action: \"check-in\",\n        time: \"8:15 AM\",\n        course: \"BSIT 3A\",\n        location: \"Main Building\"\n    },\n    {\n        id: \"2\",\n        studentName: \"Maria Santos\",\n        studentId: \"2021-001235\",\n        action: \"check-in\",\n        time: \"8:12 AM\",\n        course: \"BSBA 2B\",\n        location: \"Business Building\"\n    },\n    {\n        id: \"3\",\n        studentName: \"Pedro Garcia\",\n        studentId: \"2021-001236\",\n        action: \"late-arrival\",\n        time: \"8:35 AM\",\n        course: \"BSIT 4A\",\n        location: \"IT Building\"\n    },\n    {\n        id: \"4\",\n        studentName: \"Ana Rodriguez\",\n        studentId: \"2021-001237\",\n        action: \"check-in\",\n        time: \"8:08 AM\",\n        course: \"BSBA 1A\",\n        location: \"Main Building\"\n    },\n    {\n        id: \"5\",\n        studentName: \"Carlos Mendoza\",\n        studentId: \"2021-001238\",\n        action: \"check-out\",\n        time: \"5:30 PM\",\n        course: \"BSIT 2B\",\n        location: \"IT Building\"\n    }\n];\nconst mockAtRiskStudents = [\n    {\n        id: \"1\",\n        name: \"Roberto Silva\",\n        studentId: \"2021-001240\",\n        course: \"BSIT 1A\",\n        attendanceRate: 45,\n        consecutiveAbsences: 5,\n        lastAttendance: \"3 days ago\",\n        riskLevel: \"high\"\n    },\n    {\n        id: \"2\",\n        name: \"Lisa Chen\",\n        studentId: \"2021-001241\",\n        course: \"BSBA 3B\",\n        attendanceRate: 62,\n        consecutiveAbsences: 3,\n        lastAttendance: \"2 days ago\",\n        riskLevel: \"medium\"\n    },\n    {\n        id: \"3\",\n        name: \"Mark Johnson\",\n        studentId: \"2021-001242\",\n        course: \"BSIT 2A\",\n        attendanceRate: 71,\n        consecutiveAbsences: 2,\n        lastAttendance: \"1 day ago\",\n        riskLevel: \"low\"\n    }\n];\nconst mockUpcomingClasses = [\n    {\n        id: \"1\",\n        subject: \"Database Systems\",\n        instructor: \"Prof. Martinez\",\n        time: \"9:00 AM\",\n        room: \"IT-201\",\n        course: \"BSIT 3A\",\n        expectedStudents: 35\n    },\n    {\n        id: \"2\",\n        subject: \"Business Ethics\",\n        instructor: \"Dr. Reyes\",\n        time: \"10:30 AM\",\n        room: \"BUS-105\",\n        course: \"BSBA 2B\",\n        expectedStudents: 42\n    },\n    {\n        id: \"3\",\n        subject: \"Web Development\",\n        instructor: \"Engr. Santos\",\n        time: \"1:00 PM\",\n        room: \"IT-301\",\n        course: \"BSIT 4A\",\n        expectedStudents: 28\n    }\n];\nconst mockNotifications = [\n    {\n        id: \"1\",\n        type: \"warning\",\n        title: \"Low Attendance Alert\",\n        message: \"BSIT 1A has 65% attendance rate today\",\n        timestamp: \"10 minutes ago\",\n        isRead: false,\n        actionRequired: true\n    },\n    {\n        id: \"2\",\n        type: \"info\",\n        title: \"System Update\",\n        message: \"QR scanner firmware updated successfully\",\n        timestamp: \"1 hour ago\",\n        isRead: false\n    },\n    {\n        id: \"3\",\n        type: \"success\",\n        title: \"Report Generated\",\n        message: \"Weekly attendance report is ready\",\n        timestamp: \"2 hours ago\",\n        isRead: true\n    }\n];\nconst mockTrendData = [\n    {\n        date: \"Mon\",\n        present: 1150,\n        absent: 84,\n        late: 15,\n        attendanceRate: 92.1\n    },\n    {\n        date: \"Tue\",\n        present: 1089,\n        absent: 145,\n        late: 23,\n        attendanceRate: 88.2\n    },\n    {\n        date: \"Wed\",\n        present: 1200,\n        absent: 34,\n        late: 8,\n        attendanceRate: 96.8\n    },\n    {\n        date: \"Thu\",\n        present: 1156,\n        absent: 78,\n        late: 19,\n        attendanceRate: 93.7\n    },\n    {\n        date: \"Fri\",\n        present: 1098,\n        absent: 136,\n        late: 28,\n        attendanceRate: 89.0\n    },\n    {\n        date: \"Sat\",\n        present: 856,\n        absent: 378,\n        late: 12,\n        attendanceRate: 69.4\n    },\n    {\n        date: \"Sun\",\n        present: 234,\n        absent: 1000,\n        late: 3,\n        attendanceRate: 19.0\n    }\n];\nfunction DashboardPage() {\n    _s();\n    const [currentTime, setCurrentTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Update time every minute\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const timer = setInterval(()=>{\n            setCurrentTime(new Date());\n        }, 60000);\n        return ()=>clearInterval(timer);\n    }, []);\n    const formatTime = (date)=>{\n        return date.toLocaleTimeString(\"en-US\", {\n            hour: \"2-digit\",\n            minute: \"2-digit\",\n            hour12: true\n        });\n    };\n    const formatDate = (date)=>{\n        return date.toLocaleDateString(\"en-US\", {\n            weekday: \"long\",\n            year: \"numeric\",\n            month: \"long\",\n            day: \"numeric\"\n        });\n    };\n    const getWeatherIcon = (condition)=>{\n        switch(condition.toLowerCase()){\n            case \"sunny\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bell_BookOpen_Calendar_Clock_Cloud_CloudRain_FileText_MapPin_MessageSquare_QrCode_RefreshCw_Search_Sun_TrendingUp_UserCheck_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    className: \"h-5 w-5\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                    lineNumber: 245,\n                    columnNumber: 16\n                }, this);\n            case \"partly cloudy\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bell_BookOpen_Calendar_Clock_Cloud_CloudRain_FileText_MapPin_MessageSquare_QrCode_RefreshCw_Search_Sun_TrendingUp_UserCheck_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    className: \"h-5 w-5\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                    lineNumber: 247,\n                    columnNumber: 16\n                }, this);\n            case \"rainy\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bell_BookOpen_Calendar_Clock_Cloud_CloudRain_FileText_MapPin_MessageSquare_QrCode_RefreshCw_Search_Sun_TrendingUp_UserCheck_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    className: \"h-5 w-5\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                    lineNumber: 249,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bell_BookOpen_Calendar_Clock_Cloud_CloudRain_FileText_MapPin_MessageSquare_QrCode_RefreshCw_Search_Sun_TrendingUp_UserCheck_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    className: \"h-5 w-5\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                    lineNumber: 251,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getActionBadgeColor = (action)=>{\n        switch(action){\n            case \"check-in\":\n                return \"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200\";\n            case \"check-out\":\n                return \"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200\";\n            case \"late-arrival\":\n                return \"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200\";\n            default:\n                return \"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200\";\n        }\n    };\n    const getRiskLevelColor = (level)=>{\n        switch(level){\n            case \"high\":\n                return \"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200\";\n            case \"medium\":\n                return \"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200\";\n            case \"low\":\n                return \"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200\";\n            default:\n                return \"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200\";\n        }\n    };\n    const handleRefresh = ()=>{\n        setIsLoading(true);\n        // Simulate data refresh\n        setTimeout(()=>{\n            setIsLoading(false);\n        }, 1000);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_main_layout__WEBPACK_IMPORTED_MODULE_2__.MainLayout, {\n        title: \"Dashboard\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"education-gradient rounded-lg p-6 text-white\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold mb-2\",\n                                        children: \"Welcome to TSAT Attendance System\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                        lineNumber: 296,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-blue-100 text-lg\",\n                                        children: formatDate(currentTime)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                        lineNumber: 299,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                lineNumber: 295,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-right\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold\",\n                                                children: formatTime(currentTime)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                lineNumber: 305,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-blue-100 text-sm\",\n                                                children: \"Current Time\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                lineNumber: 306,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                        lineNumber: 304,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_8__.Separator, {\n                                        orientation: \"vertical\",\n                                        className: \"h-12 bg-blue-300\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                        lineNumber: 308,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-white\",\n                                                children: getWeatherIcon(mockWeather.condition)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                lineNumber: 310,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xl font-semibold\",\n                                                        children: [\n                                                            mockWeather.temperature,\n                                                            \"\\xb0C\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                        lineNumber: 314,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-blue-100 text-sm\",\n                                                        children: mockWeather.condition\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                        lineNumber: 315,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                lineNumber: 313,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                        lineNumber: 309,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                lineNumber: 303,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                        lineNumber: 294,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                    lineNumber: 293,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"pt-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bell_BookOpen_Calendar_Clock_Cloud_CloudRain_FileText_MapPin_MessageSquare_QrCode_RefreshCw_Search_Sun_TrendingUp_UserCheck_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                            lineNumber: 327,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_9__.Input, {\n                                            placeholder: \"Search students by name or ID...\",\n                                            value: searchQuery,\n                                            onChange: (e)=>setSearchQuery(e.target.value),\n                                            className: \"pl-10\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                            lineNumber: 328,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                    lineNumber: 326,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: handleRefresh,\n                                    disabled: isLoading,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bell_BookOpen_Calendar_Clock_Cloud_CloudRain_FileText_MapPin_MessageSquare_QrCode_RefreshCw_Search_Sun_TrendingUp_UserCheck_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2 \".concat(isLoading ? \"animate-spin\" : \"\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                            lineNumber: 336,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Refresh\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                    lineNumber: 335,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                            lineNumber: 325,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                        lineNumber: 324,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                    lineNumber: 323,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid gap-4 md:grid-cols-2 lg:grid-cols-5\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Total Attendance\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                            lineNumber: 347,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bell_BookOpen_Calendar_Clock_Cloud_CloudRain_FileText_MapPin_MessageSquare_QrCode_RefreshCw_Search_Sun_TrendingUp_UserCheck_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-4 w-4 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                            lineNumber: 348,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                    lineNumber: 346,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: mockStats.totalStudents.toLocaleString()\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                            lineNumber: 351,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: \"Active students\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                            lineNumber: 352,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                    lineNumber: 350,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                            lineNumber: 345,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Present Today\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                            lineNumber: 358,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bell_BookOpen_Calendar_Clock_Cloud_CloudRain_FileText_MapPin_MessageSquare_QrCode_RefreshCw_Search_Sun_TrendingUp_UserCheck_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-4 w-4 text-green-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                            lineNumber: 359,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                    lineNumber: 357,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-green-600\",\n                                            children: mockStats.presentToday.toLocaleString()\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                            lineNumber: 362,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: \"Students checked in\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                            lineNumber: 363,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                    lineNumber: 361,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                            lineNumber: 356,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Absent Today\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                            lineNumber: 369,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bell_BookOpen_Calendar_Clock_Cloud_CloudRain_FileText_MapPin_MessageSquare_QrCode_RefreshCw_Search_Sun_TrendingUp_UserCheck_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"h-4 w-4 text-red-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                            lineNumber: 370,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                    lineNumber: 368,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-red-600\",\n                                            children: mockStats.absentToday\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                            lineNumber: 373,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: \"Students absent\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                            lineNumber: 374,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                    lineNumber: 372,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                            lineNumber: 367,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Late Arrivals\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                            lineNumber: 380,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bell_BookOpen_Calendar_Clock_Cloud_CloudRain_FileText_MapPin_MessageSquare_QrCode_RefreshCw_Search_Sun_TrendingUp_UserCheck_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            className: \"h-4 w-4 text-yellow-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                            lineNumber: 381,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                    lineNumber: 379,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-yellow-600\",\n                                            children: mockStats.lateToday\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                            lineNumber: 384,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: \"Late students\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                            lineNumber: 385,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                    lineNumber: 383,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                            lineNumber: 378,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Attendance Rate\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                            lineNumber: 391,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bell_BookOpen_Calendar_Clock_Cloud_CloudRain_FileText_MapPin_MessageSquare_QrCode_RefreshCw_Search_Sun_TrendingUp_UserCheck_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            className: \"h-4 w-4 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                            lineNumber: 392,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                    lineNumber: 390,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-blue-600\",\n                                            children: [\n                                                mockStats.attendanceRate,\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                            lineNumber: 395,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_6__.Progress, {\n                                            value: mockStats.attendanceRate,\n                                            className: \"mt-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                            lineNumber: 396,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                    lineNumber: 394,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                            lineNumber: 389,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                    lineNumber: 344,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid gap-4 md:grid-cols-2 lg:grid-cols-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            size: \"lg\",\n                            className: \"h-20 flex-col gap-2 bg-blue-600 hover:bg-blue-700\",\n                            asChild: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"/scan\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bell_BookOpen_Calendar_Clock_Cloud_CloudRain_FileText_MapPin_MessageSquare_QrCode_RefreshCw_Search_Sun_TrendingUp_UserCheck_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        className: \"h-8 w-8\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                        lineNumber: 409,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-lg font-semibold\",\n                                        children: \"Scan QR Code\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                        lineNumber: 410,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                lineNumber: 408,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                            lineNumber: 403,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            variant: \"outline\",\n                            size: \"lg\",\n                            className: \"h-20 flex-col gap-2\",\n                            asChild: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"/reports\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bell_BookOpen_Calendar_Clock_Cloud_CloudRain_FileText_MapPin_MessageSquare_QrCode_RefreshCw_Search_Sun_TrendingUp_UserCheck_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                        className: \"h-6 w-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                        lineNumber: 421,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"View Today's Reports\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                        lineNumber: 422,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                lineNumber: 420,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                            lineNumber: 414,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            variant: \"outline\",\n                            size: \"lg\",\n                            className: \"h-20 flex-col gap-2\",\n                            disabled: true,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bell_BookOpen_Calendar_Clock_Cloud_CloudRain_FileText_MapPin_MessageSquare_QrCode_RefreshCw_Search_Sun_TrendingUp_UserCheck_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    className: \"h-6 w-6\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                    lineNumber: 432,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Generate SF2 Form\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                    lineNumber: 433,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                            lineNumber: 426,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            variant: \"outline\",\n                            size: \"lg\",\n                            className: \"h-20 flex-col gap-2\",\n                            disabled: true,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bell_BookOpen_Calendar_Clock_Cloud_CloudRain_FileText_MapPin_MessageSquare_QrCode_RefreshCw_Search_Sun_TrendingUp_UserCheck_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                    className: \"h-6 w-6\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                    lineNumber: 442,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Send SMS Notifications\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                    lineNumber: 443,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                            lineNumber: 436,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                    lineNumber: 402,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid gap-6 lg:grid-cols-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"lg:col-span-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bell_BookOpen_Calendar_Clock_Cloud_CloudRain_FileText_MapPin_MessageSquare_QrCode_RefreshCw_Search_Sun_TrendingUp_UserCheck_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                    lineNumber: 453,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Recent Activity\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                            lineNumber: 452,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                            children: \"Latest attendance scans and student check-ins\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                            lineNumber: 456,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                    lineNumber: 451,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4 max-h-96 overflow-y-auto\",\n                                        children: mockRecentActivity.map((activity)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between p-4 rounded-lg border bg-card\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-10 h-10 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bell_BookOpen_Calendar_Clock_Cloud_CloudRain_FileText_MapPin_MessageSquare_QrCode_RefreshCw_Search_Sun_TrendingUp_UserCheck_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"h-5 w-5 text-blue-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 466,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                                lineNumber: 465,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-medium\",\n                                                                        children: activity.studentName\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 469,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-muted-foreground\",\n                                                                        children: [\n                                                                            activity.studentId,\n                                                                            \" • \",\n                                                                            activity.course\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 470,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    activity.location && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-muted-foreground flex items-center gap-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bell_BookOpen_Calendar_Clock_Cloud_CloudRain_FileText_MapPin_MessageSquare_QrCode_RefreshCw_Search_Sun_TrendingUp_UserCheck_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                                className: \"h-3 w-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 475,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            activity.location\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 474,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                                lineNumber: 468,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                        lineNumber: 464,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-right\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                className: getActionBadgeColor(activity.action),\n                                                                children: activity.action.replace(\"-\", \" \")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                                lineNumber: 482,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-muted-foreground mt-1\",\n                                                                children: activity.time\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                                lineNumber: 485,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                        lineNumber: 481,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, activity.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                lineNumber: 463,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                        lineNumber: 461,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                    lineNumber: 460,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                            lineNumber: 450,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bell_BookOpen_Calendar_Clock_Cloud_CloudRain_FileText_MapPin_MessageSquare_QrCode_RefreshCw_Search_Sun_TrendingUp_UserCheck_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                    lineNumber: 499,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"System Notifications\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                            lineNumber: 498,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                            children: \"Important alerts and updates\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                            lineNumber: 502,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                    lineNumber: 497,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3 max-h-96 overflow-y-auto\",\n                                        children: mockNotifications.map((notification)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_7__.Alert, {\n                                                className: notification.type === \"warning\" ? \"border-yellow-200 bg-yellow-50 dark:border-yellow-800 dark:bg-yellow-950\" : notification.type === \"error\" ? \"border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-950\" : notification.type === \"success\" ? \"border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950\" : \"border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-950\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bell_BookOpen_Calendar_Clock_Cloud_CloudRain_FileText_MapPin_MessageSquare_QrCode_RefreshCw_Search_Sun_TrendingUp_UserCheck_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                        lineNumber: 515,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_7__.AlertTitle, {\n                                                        className: \"text-sm\",\n                                                        children: notification.title\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                        lineNumber: 516,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_7__.AlertDescription, {\n                                                        className: \"text-xs\",\n                                                        children: [\n                                                            notification.message,\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-muted-foreground mt-1\",\n                                                                children: notification.timestamp\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                                lineNumber: 519,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                        lineNumber: 517,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, notification.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                lineNumber: 509,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                        lineNumber: 507,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                    lineNumber: 506,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                            lineNumber: 496,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                    lineNumber: 448,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid gap-6 lg:grid-cols-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-950\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"flex items-center gap-2 text-red-800 dark:text-red-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bell_BookOpen_Calendar_Clock_Cloud_CloudRain_FileText_MapPin_MessageSquare_QrCode_RefreshCw_Search_Sun_TrendingUp_UserCheck_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                    lineNumber: 536,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"At-Risk Students\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                            lineNumber: 535,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                            className: \"text-red-600 dark:text-red-300\",\n                                            children: \"Students requiring immediate attention\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                            lineNumber: 539,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                    lineNumber: 534,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: mockAtRiskStudents.map((student)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-4 rounded-lg bg-white dark:bg-gray-900 border border-red-200 dark:border-red-800\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between mb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-medium\",\n                                                                        children: student.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 549,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-muted-foreground\",\n                                                                        children: [\n                                                                            student.studentId,\n                                                                            \" • \",\n                                                                            student.course\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 550,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                                lineNumber: 548,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                className: getRiskLevelColor(student.riskLevel),\n                                                                children: [\n                                                                    student.riskLevel,\n                                                                    \" risk\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                                lineNumber: 554,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                        lineNumber: 547,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-2 gap-4 text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-muted-foreground\",\n                                                                        children: \"Attendance Rate\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 560,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-medium\",\n                                                                        children: [\n                                                                            student.attendanceRate,\n                                                                            \"%\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 561,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                                lineNumber: 559,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-muted-foreground\",\n                                                                        children: \"Consecutive Absences\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 564,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-medium\",\n                                                                        children: [\n                                                                            student.consecutiveAbsences,\n                                                                            \" days\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 565,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                                lineNumber: 563,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                        lineNumber: 558,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-muted-foreground mt-2\",\n                                                        children: [\n                                                            \"Last attendance: \",\n                                                            student.lastAttendance\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                        lineNumber: 568,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, student.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                lineNumber: 546,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                        lineNumber: 544,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                    lineNumber: 543,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                            lineNumber: 533,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bell_BookOpen_Calendar_Clock_Cloud_CloudRain_FileText_MapPin_MessageSquare_QrCode_RefreshCw_Search_Sun_TrendingUp_UserCheck_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                    lineNumber: 581,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Upcoming Classes\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                            lineNumber: 580,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                            children: \"Today's scheduled classes and expected attendance\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                            lineNumber: 584,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                    lineNumber: 579,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: mockUpcomingClasses.map((classItem)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-4 rounded-lg border bg-card\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between mb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-medium\",\n                                                                        children: classItem.subject\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 594,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-muted-foreground\",\n                                                                        children: classItem.instructor\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 595,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                                lineNumber: 593,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-right\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-medium\",\n                                                                        children: classItem.time\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 600,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-muted-foreground\",\n                                                                        children: classItem.room\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 601,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                                lineNumber: 599,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                        lineNumber: 592,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                variant: \"outline\",\n                                                                children: classItem.course\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                                lineNumber: 607,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-muted-foreground\",\n                                                                children: [\n                                                                    \"Expected: \",\n                                                                    classItem.expectedStudents,\n                                                                    \" students\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                                lineNumber: 608,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                        lineNumber: 606,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, classItem.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                lineNumber: 591,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                        lineNumber: 589,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                    lineNumber: 588,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                            lineNumber: 578,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                    lineNumber: 531,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bell_BookOpen_Calendar_Clock_Cloud_CloudRain_FileText_MapPin_MessageSquare_QrCode_RefreshCw_Search_Sun_TrendingUp_UserCheck_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                            lineNumber: 623,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Attendance Trends\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                    lineNumber: 622,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                    children: \"Weekly attendance patterns and trends\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                    lineNumber: 626,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                            lineNumber: 621,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-80\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_29__.ResponsiveContainer, {\n                                    width: \"100%\",\n                                    height: \"100%\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_30__.LineChart, {\n                                        data: mockTrendData,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_31__.CartesianGrid, {\n                                                strokeDasharray: \"3 3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                lineNumber: 634,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_32__.XAxis, {\n                                                dataKey: \"date\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                lineNumber: 635,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_33__.YAxis, {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                lineNumber: 636,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_34__.Tooltip, {\n                                                content: (param)=>{\n                                                    let { active, payload, label } = param;\n                                                    if (active && payload && payload.length) {\n                                                        var _payload_, _payload_1, _payload_2, _payload_3;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-white dark:bg-gray-800 p-3 border rounded-lg shadow-lg\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium\",\n                                                                    children: label\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 642,\n                                                                    columnNumber: 29\n                                                                }, void 0),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-green-600\",\n                                                                    children: [\n                                                                        \"Present: \",\n                                                                        (_payload_ = payload[0]) === null || _payload_ === void 0 ? void 0 : _payload_.value\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 643,\n                                                                    columnNumber: 29\n                                                                }, void 0),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-red-600\",\n                                                                    children: [\n                                                                        \"Absent: \",\n                                                                        (_payload_1 = payload[1]) === null || _payload_1 === void 0 ? void 0 : _payload_1.value\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 646,\n                                                                    columnNumber: 29\n                                                                }, void 0),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-yellow-600\",\n                                                                    children: [\n                                                                        \"Late: \",\n                                                                        (_payload_2 = payload[2]) === null || _payload_2 === void 0 ? void 0 : _payload_2.value\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 649,\n                                                                    columnNumber: 29\n                                                                }, void 0),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-blue-600\",\n                                                                    children: [\n                                                                        \"Rate: \",\n                                                                        (_payload_3 = payload[3]) === null || _payload_3 === void 0 ? void 0 : _payload_3.value,\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 652,\n                                                                    columnNumber: 29\n                                                                }, void 0)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                            lineNumber: 641,\n                                                            columnNumber: 27\n                                                        }, void 0);\n                                                    }\n                                                    return null;\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                lineNumber: 637,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_35__.Line, {\n                                                type: \"monotone\",\n                                                dataKey: \"present\",\n                                                stroke: \"#10b981\",\n                                                strokeWidth: 2,\n                                                name: \"Present\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                lineNumber: 661,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_35__.Line, {\n                                                type: \"monotone\",\n                                                dataKey: \"absent\",\n                                                stroke: \"#ef4444\",\n                                                strokeWidth: 2,\n                                                name: \"Absent\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                lineNumber: 668,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_35__.Line, {\n                                                type: \"monotone\",\n                                                dataKey: \"late\",\n                                                stroke: \"#f59e0b\",\n                                                strokeWidth: 2,\n                                                name: \"Late\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                lineNumber: 675,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_35__.Line, {\n                                                type: \"monotone\",\n                                                dataKey: \"attendanceRate\",\n                                                stroke: \"#3b82f6\",\n                                                strokeWidth: 3,\n                                                name: \"Attendance Rate\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                lineNumber: 682,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                        lineNumber: 633,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                    lineNumber: 632,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                lineNumber: 631,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                            lineNumber: 630,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                    lineNumber: 620,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n            lineNumber: 291,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n        lineNumber: 290,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardPage, \"YfzYF5/v9NTOL2IKwUnIUmf8H/Y=\");\n_c = DashboardPage;\nvar _c;\n$RefreshReg$(_c, \"DashboardPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});