"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DashboardPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_layout_main_layout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/main-layout */ \"(app-pages-browser)/./components/layout/main-layout.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_progress__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/progress */ \"(app-pages-browser)/./components/ui/progress.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./components/ui/alert.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./components/ui/separator.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bell_BookOpen_Calendar_Clock_Cloud_CloudRain_FileText_MapPin_MessageSquare_QrCode_RefreshCw_Search_Sun_TrendingUp_UserCheck_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bell,BookOpen,Calendar,Clock,Cloud,CloudRain,FileText,MapPin,MessageSquare,QrCode,RefreshCw,Search,Sun,TrendingUp,UserCheck,UserX,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sun.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bell_BookOpen_Calendar_Clock_Cloud_CloudRain_FileText_MapPin_MessageSquare_QrCode_RefreshCw_Search_Sun_TrendingUp_UserCheck_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bell,BookOpen,Calendar,Clock,Cloud,CloudRain,FileText,MapPin,MessageSquare,QrCode,RefreshCw,Search,Sun,TrendingUp,UserCheck,UserX,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/cloud.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bell_BookOpen_Calendar_Clock_Cloud_CloudRain_FileText_MapPin_MessageSquare_QrCode_RefreshCw_Search_Sun_TrendingUp_UserCheck_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bell,BookOpen,Calendar,Clock,Cloud,CloudRain,FileText,MapPin,MessageSquare,QrCode,RefreshCw,Search,Sun,TrendingUp,UserCheck,UserX,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/cloud-rain.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bell_BookOpen_Calendar_Clock_Cloud_CloudRain_FileText_MapPin_MessageSquare_QrCode_RefreshCw_Search_Sun_TrendingUp_UserCheck_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bell,BookOpen,Calendar,Clock,Cloud,CloudRain,FileText,MapPin,MessageSquare,QrCode,RefreshCw,Search,Sun,TrendingUp,UserCheck,UserX,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bell_BookOpen_Calendar_Clock_Cloud_CloudRain_FileText_MapPin_MessageSquare_QrCode_RefreshCw_Search_Sun_TrendingUp_UserCheck_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bell,BookOpen,Calendar,Clock,Cloud,CloudRain,FileText,MapPin,MessageSquare,QrCode,RefreshCw,Search,Sun,TrendingUp,UserCheck,UserX,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bell_BookOpen_Calendar_Clock_Cloud_CloudRain_FileText_MapPin_MessageSquare_QrCode_RefreshCw_Search_Sun_TrendingUp_UserCheck_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bell,BookOpen,Calendar,Clock,Cloud,CloudRain,FileText,MapPin,MessageSquare,QrCode,RefreshCw,Search,Sun,TrendingUp,UserCheck,UserX,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bell_BookOpen_Calendar_Clock_Cloud_CloudRain_FileText_MapPin_MessageSquare_QrCode_RefreshCw_Search_Sun_TrendingUp_UserCheck_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bell,BookOpen,Calendar,Clock,Cloud,CloudRain,FileText,MapPin,MessageSquare,QrCode,RefreshCw,Search,Sun,TrendingUp,UserCheck,UserX,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-check.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bell_BookOpen_Calendar_Clock_Cloud_CloudRain_FileText_MapPin_MessageSquare_QrCode_RefreshCw_Search_Sun_TrendingUp_UserCheck_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bell,BookOpen,Calendar,Clock,Cloud,CloudRain,FileText,MapPin,MessageSquare,QrCode,RefreshCw,Search,Sun,TrendingUp,UserCheck,UserX,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-x.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bell_BookOpen_Calendar_Clock_Cloud_CloudRain_FileText_MapPin_MessageSquare_QrCode_RefreshCw_Search_Sun_TrendingUp_UserCheck_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bell,BookOpen,Calendar,Clock,Cloud,CloudRain,FileText,MapPin,MessageSquare,QrCode,RefreshCw,Search,Sun,TrendingUp,UserCheck,UserX,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bell_BookOpen_Calendar_Clock_Cloud_CloudRain_FileText_MapPin_MessageSquare_QrCode_RefreshCw_Search_Sun_TrendingUp_UserCheck_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bell,BookOpen,Calendar,Clock,Cloud,CloudRain,FileText,MapPin,MessageSquare,QrCode,RefreshCw,Search,Sun,TrendingUp,UserCheck,UserX,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bell_BookOpen_Calendar_Clock_Cloud_CloudRain_FileText_MapPin_MessageSquare_QrCode_RefreshCw_Search_Sun_TrendingUp_UserCheck_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bell,BookOpen,Calendar,Clock,Cloud,CloudRain,FileText,MapPin,MessageSquare,QrCode,RefreshCw,Search,Sun,TrendingUp,UserCheck,UserX,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/qr-code.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bell_BookOpen_Calendar_Clock_Cloud_CloudRain_FileText_MapPin_MessageSquare_QrCode_RefreshCw_Search_Sun_TrendingUp_UserCheck_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bell,BookOpen,Calendar,Clock,Cloud,CloudRain,FileText,MapPin,MessageSquare,QrCode,RefreshCw,Search,Sun,TrendingUp,UserCheck,UserX,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bell_BookOpen_Calendar_Clock_Cloud_CloudRain_FileText_MapPin_MessageSquare_QrCode_RefreshCw_Search_Sun_TrendingUp_UserCheck_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bell,BookOpen,Calendar,Clock,Cloud,CloudRain,FileText,MapPin,MessageSquare,QrCode,RefreshCw,Search,Sun,TrendingUp,UserCheck,UserX,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bell_BookOpen_Calendar_Clock_Cloud_CloudRain_FileText_MapPin_MessageSquare_QrCode_RefreshCw_Search_Sun_TrendingUp_UserCheck_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bell,BookOpen,Calendar,Clock,Cloud,CloudRain,FileText,MapPin,MessageSquare,QrCode,RefreshCw,Search,Sun,TrendingUp,UserCheck,UserX,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bell_BookOpen_Calendar_Clock_Cloud_CloudRain_FileText_MapPin_MessageSquare_QrCode_RefreshCw_Search_Sun_TrendingUp_UserCheck_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bell,BookOpen,Calendar,Clock,Cloud,CloudRain,FileText,MapPin,MessageSquare,QrCode,RefreshCw,Search,Sun,TrendingUp,UserCheck,UserX,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bell_BookOpen_Calendar_Clock_Cloud_CloudRain_FileText_MapPin_MessageSquare_QrCode_RefreshCw_Search_Sun_TrendingUp_UserCheck_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bell,BookOpen,Calendar,Clock,Cloud,CloudRain,FileText,MapPin,MessageSquare,QrCode,RefreshCw,Search,Sun,TrendingUp,UserCheck,UserX,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bell_BookOpen_Calendar_Clock_Cloud_CloudRain_FileText_MapPin_MessageSquare_QrCode_RefreshCw_Search_Sun_TrendingUp_UserCheck_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bell,BookOpen,Calendar,Clock,Cloud,CloudRain,FileText,MapPin,MessageSquare,QrCode,RefreshCw,Search,Sun,TrendingUp,UserCheck,UserX,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bell_BookOpen_Calendar_Clock_Cloud_CloudRain_FileText_MapPin_MessageSquare_QrCode_RefreshCw_Search_Sun_TrendingUp_UserCheck_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bell,BookOpen,Calendar,Clock,Cloud,CloudRain,FileText,MapPin,MessageSquare,QrCode,RefreshCw,Search,Sun,TrendingUp,UserCheck,UserX,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bell_BookOpen_Calendar_Clock_Cloud_CloudRain_FileText_MapPin_MessageSquare_QrCode_RefreshCw_Search_Sun_TrendingUp_UserCheck_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bell,BookOpen,Calendar,Clock,Cloud,CloudRain,FileText,MapPin,MessageSquare,QrCode,RefreshCw,Search,Sun,TrendingUp,UserCheck,UserX,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/component/ResponsiveContainer.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/chart/LineChart.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/CartesianGrid.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/XAxis.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/YAxis.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/component/Tooltip.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/Line.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n// Mock data for the dashboard\nconst mockStats = {\n    totalStudents: 1234,\n    presentToday: 1089,\n    absentToday: 145,\n    lateToday: 23,\n    attendanceRate: 88.2,\n    totalCourses: 12,\n    activeSections: 45\n};\nconst mockWeather = {\n    temperature: 28,\n    condition: \"Partly Cloudy\",\n    humidity: 65,\n    icon: \"partly-cloudy\"\n};\nconst mockRecentActivity = [\n    {\n        id: \"1\",\n        studentName: \"Juan Dela Cruz\",\n        studentId: \"2021-001234\",\n        action: \"check-in\",\n        time: \"8:15 AM\",\n        course: \"BSIT 3A\",\n        location: \"Main Building\"\n    },\n    {\n        id: \"2\",\n        studentName: \"Maria Santos\",\n        studentId: \"2021-001235\",\n        action: \"check-in\",\n        time: \"8:12 AM\",\n        course: \"BSBA 2B\",\n        location: \"Business Building\"\n    },\n    {\n        id: \"3\",\n        studentName: \"Pedro Garcia\",\n        studentId: \"2021-001236\",\n        action: \"late-arrival\",\n        time: \"8:35 AM\",\n        course: \"BSIT 4A\",\n        location: \"IT Building\"\n    },\n    {\n        id: \"4\",\n        studentName: \"Ana Rodriguez\",\n        studentId: \"2021-001237\",\n        action: \"check-in\",\n        time: \"8:08 AM\",\n        course: \"BSBA 1A\",\n        location: \"Main Building\"\n    },\n    {\n        id: \"5\",\n        studentName: \"Carlos Mendoza\",\n        studentId: \"2021-001238\",\n        action: \"check-out\",\n        time: \"5:30 PM\",\n        course: \"BSIT 2B\",\n        location: \"IT Building\"\n    }\n];\nconst mockAtRiskStudents = [\n    {\n        id: \"1\",\n        name: \"Roberto Silva\",\n        studentId: \"2021-001240\",\n        course: \"BSIT 1A\",\n        attendanceRate: 45,\n        consecutiveAbsences: 5,\n        lastAttendance: \"3 days ago\",\n        riskLevel: \"high\"\n    },\n    {\n        id: \"2\",\n        name: \"Lisa Chen\",\n        studentId: \"2021-001241\",\n        course: \"BSBA 3B\",\n        attendanceRate: 62,\n        consecutiveAbsences: 3,\n        lastAttendance: \"2 days ago\",\n        riskLevel: \"medium\"\n    },\n    {\n        id: \"3\",\n        name: \"Mark Johnson\",\n        studentId: \"2021-001242\",\n        course: \"BSIT 2A\",\n        attendanceRate: 71,\n        consecutiveAbsences: 2,\n        lastAttendance: \"1 day ago\",\n        riskLevel: \"low\"\n    }\n];\nconst mockUpcomingClasses = [\n    {\n        id: \"1\",\n        subject: \"Database Systems\",\n        instructor: \"Prof. Martinez\",\n        time: \"9:00 AM\",\n        room: \"IT-201\",\n        course: \"BSIT 3A\",\n        expectedStudents: 35\n    },\n    {\n        id: \"2\",\n        subject: \"Business Ethics\",\n        instructor: \"Dr. Reyes\",\n        time: \"10:30 AM\",\n        room: \"BUS-105\",\n        course: \"BSBA 2B\",\n        expectedStudents: 42\n    },\n    {\n        id: \"3\",\n        subject: \"Web Development\",\n        instructor: \"Engr. Santos\",\n        time: \"1:00 PM\",\n        room: \"IT-301\",\n        course: \"BSIT 4A\",\n        expectedStudents: 28\n    }\n];\nconst mockNotifications = [\n    {\n        id: \"1\",\n        type: \"warning\",\n        title: \"Low Attendance Alert\",\n        message: \"BSIT 1A has 65% attendance rate today\",\n        timestamp: \"10 minutes ago\",\n        isRead: false,\n        actionRequired: true\n    },\n    {\n        id: \"2\",\n        type: \"info\",\n        title: \"System Update\",\n        message: \"QR scanner firmware updated successfully\",\n        timestamp: \"1 hour ago\",\n        isRead: false\n    },\n    {\n        id: \"3\",\n        type: \"success\",\n        title: \"Report Generated\",\n        message: \"Weekly attendance report is ready\",\n        timestamp: \"2 hours ago\",\n        isRead: true\n    }\n];\nconst mockTrendData = [\n    {\n        date: \"Mon\",\n        present: 1150,\n        absent: 84,\n        late: 15,\n        attendanceRate: 92.1\n    },\n    {\n        date: \"Tue\",\n        present: 1089,\n        absent: 145,\n        late: 23,\n        attendanceRate: 88.2\n    },\n    {\n        date: \"Wed\",\n        present: 1200,\n        absent: 34,\n        late: 8,\n        attendanceRate: 96.8\n    },\n    {\n        date: \"Thu\",\n        present: 1156,\n        absent: 78,\n        late: 19,\n        attendanceRate: 93.7\n    },\n    {\n        date: \"Fri\",\n        present: 1098,\n        absent: 136,\n        late: 28,\n        attendanceRate: 89.0\n    },\n    {\n        date: \"Sat\",\n        present: 856,\n        absent: 378,\n        late: 12,\n        attendanceRate: 69.4\n    },\n    {\n        date: \"Sun\",\n        present: 234,\n        absent: 1000,\n        late: 3,\n        attendanceRate: 19.0\n    }\n];\nfunction DashboardPage() {\n    _s();\n    const [currentTime, setCurrentTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Update time every minute\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const timer = setInterval(()=>{\n            setCurrentTime(new Date());\n        }, 60000);\n        return ()=>clearInterval(timer);\n    }, []);\n    const formatTime = (date)=>{\n        return date.toLocaleTimeString(\"en-US\", {\n            hour: \"2-digit\",\n            minute: \"2-digit\",\n            hour12: true\n        });\n    };\n    const formatDate = (date)=>{\n        return date.toLocaleDateString(\"en-US\", {\n            weekday: \"long\",\n            year: \"numeric\",\n            month: \"long\",\n            day: \"numeric\"\n        });\n    };\n    const getWeatherIcon = (condition)=>{\n        switch(condition.toLowerCase()){\n            case \"sunny\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bell_BookOpen_Calendar_Clock_Cloud_CloudRain_FileText_MapPin_MessageSquare_QrCode_RefreshCw_Search_Sun_TrendingUp_UserCheck_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    className: \"h-5 w-5\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                    lineNumber: 245,\n                    columnNumber: 16\n                }, this);\n            case \"partly cloudy\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bell_BookOpen_Calendar_Clock_Cloud_CloudRain_FileText_MapPin_MessageSquare_QrCode_RefreshCw_Search_Sun_TrendingUp_UserCheck_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    className: \"h-5 w-5\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                    lineNumber: 247,\n                    columnNumber: 16\n                }, this);\n            case \"rainy\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bell_BookOpen_Calendar_Clock_Cloud_CloudRain_FileText_MapPin_MessageSquare_QrCode_RefreshCw_Search_Sun_TrendingUp_UserCheck_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    className: \"h-5 w-5\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                    lineNumber: 249,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bell_BookOpen_Calendar_Clock_Cloud_CloudRain_FileText_MapPin_MessageSquare_QrCode_RefreshCw_Search_Sun_TrendingUp_UserCheck_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    className: \"h-5 w-5\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                    lineNumber: 251,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getActionBadgeColor = (action)=>{\n        switch(action){\n            case \"check-in\":\n                return \"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200\";\n            case \"check-out\":\n                return \"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200\";\n            case \"late-arrival\":\n                return \"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200\";\n            default:\n                return \"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200\";\n        }\n    };\n    const getRiskLevelColor = (level)=>{\n        switch(level){\n            case \"high\":\n                return \"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200\";\n            case \"medium\":\n                return \"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200\";\n            case \"low\":\n                return \"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200\";\n            default:\n                return \"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200\";\n        }\n    };\n    const handleRefresh = ()=>{\n        setIsLoading(true);\n        // Simulate data refresh\n        setTimeout(()=>{\n            setIsLoading(false);\n        }, 1000);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_main_layout__WEBPACK_IMPORTED_MODULE_2__.MainLayout, {\n        title: \"Dashboard\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"education-gradient rounded-lg p-6 text-white\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold mb-2\",\n                                        children: \"Welcome to TSAT Attendance System\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                        lineNumber: 296,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-blue-100 text-lg\",\n                                        children: formatDate(currentTime)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                        lineNumber: 299,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                lineNumber: 295,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-right\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold\",\n                                                children: formatTime(currentTime)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                lineNumber: 305,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-blue-100 text-sm\",\n                                                children: \"Current Time\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                lineNumber: 306,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                        lineNumber: 304,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_8__.Separator, {\n                                        orientation: \"vertical\",\n                                        className: \"h-12 bg-blue-300\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                        lineNumber: 308,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-white\",\n                                                children: getWeatherIcon(mockWeather.condition)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                lineNumber: 310,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xl font-semibold\",\n                                                        children: [\n                                                            mockWeather.temperature,\n                                                            \"\\xb0C\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                        lineNumber: 314,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-blue-100 text-sm\",\n                                                        children: mockWeather.condition\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                        lineNumber: 315,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                lineNumber: 313,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                        lineNumber: 309,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                lineNumber: 303,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                        lineNumber: 294,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                    lineNumber: 293,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"pt-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bell_BookOpen_Calendar_Clock_Cloud_CloudRain_FileText_MapPin_MessageSquare_QrCode_RefreshCw_Search_Sun_TrendingUp_UserCheck_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                            lineNumber: 327,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_9__.Input, {\n                                            placeholder: \"Search students by name or ID...\",\n                                            value: searchQuery,\n                                            onChange: (e)=>setSearchQuery(e.target.value),\n                                            className: \"pl-10\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                            lineNumber: 328,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                    lineNumber: 326,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    disabled: true,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bell_BookOpen_Calendar_Clock_Cloud_CloudRain_FileText_MapPin_MessageSquare_QrCode_RefreshCw_Search_Sun_TrendingUp_UserCheck_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                            lineNumber: 336,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Refresh\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                    lineNumber: 335,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                            lineNumber: 325,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                        lineNumber: 324,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                    lineNumber: 323,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid gap-4 md:grid-cols-2 lg:grid-cols-5\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Total Attendance\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                            lineNumber: 347,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bell_BookOpen_Calendar_Clock_Cloud_CloudRain_FileText_MapPin_MessageSquare_QrCode_RefreshCw_Search_Sun_TrendingUp_UserCheck_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-4 w-4 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                            lineNumber: 348,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                    lineNumber: 346,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: mockStats.totalStudents.toLocaleString()\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                            lineNumber: 351,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: \"Active students\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                            lineNumber: 352,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                    lineNumber: 350,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                            lineNumber: 345,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Present Today\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                            lineNumber: 358,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bell_BookOpen_Calendar_Clock_Cloud_CloudRain_FileText_MapPin_MessageSquare_QrCode_RefreshCw_Search_Sun_TrendingUp_UserCheck_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-4 w-4 text-green-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                            lineNumber: 359,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                    lineNumber: 357,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-green-600\",\n                                            children: mockStats.presentToday.toLocaleString()\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                            lineNumber: 362,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: \"Students checked in\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                            lineNumber: 363,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                    lineNumber: 361,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                            lineNumber: 356,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Absent Today\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                            lineNumber: 369,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bell_BookOpen_Calendar_Clock_Cloud_CloudRain_FileText_MapPin_MessageSquare_QrCode_RefreshCw_Search_Sun_TrendingUp_UserCheck_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"h-4 w-4 text-red-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                            lineNumber: 370,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                    lineNumber: 368,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-red-600\",\n                                            children: mockStats.absentToday\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                            lineNumber: 373,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: \"Students absent\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                            lineNumber: 374,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                    lineNumber: 372,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                            lineNumber: 367,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Late Arrivals\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                            lineNumber: 380,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bell_BookOpen_Calendar_Clock_Cloud_CloudRain_FileText_MapPin_MessageSquare_QrCode_RefreshCw_Search_Sun_TrendingUp_UserCheck_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            className: \"h-4 w-4 text-yellow-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                            lineNumber: 381,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                    lineNumber: 379,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-yellow-600\",\n                                            children: mockStats.lateToday\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                            lineNumber: 384,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: \"Late students\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                            lineNumber: 385,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                    lineNumber: 383,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                            lineNumber: 378,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Attendance Rate\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                            lineNumber: 391,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bell_BookOpen_Calendar_Clock_Cloud_CloudRain_FileText_MapPin_MessageSquare_QrCode_RefreshCw_Search_Sun_TrendingUp_UserCheck_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            className: \"h-4 w-4 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                            lineNumber: 392,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                    lineNumber: 390,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-blue-600\",\n                                            children: [\n                                                mockStats.attendanceRate,\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                            lineNumber: 395,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_6__.Progress, {\n                                            value: mockStats.attendanceRate,\n                                            className: \"mt-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                            lineNumber: 396,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                    lineNumber: 394,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                            lineNumber: 389,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                    lineNumber: 344,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid gap-4 md:grid-cols-2 lg:grid-cols-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            size: \"lg\",\n                            className: \"h-20 flex-col gap-2 bg-blue-600 hover:bg-blue-700\",\n                            asChild: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"/scan\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bell_BookOpen_Calendar_Clock_Cloud_CloudRain_FileText_MapPin_MessageSquare_QrCode_RefreshCw_Search_Sun_TrendingUp_UserCheck_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        className: \"h-8 w-8\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                        lineNumber: 409,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-lg font-semibold\",\n                                        children: \"Scan QR Code\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                        lineNumber: 410,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                lineNumber: 408,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                            lineNumber: 403,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            variant: \"outline\",\n                            size: \"lg\",\n                            className: \"h-20 flex-col gap-2\",\n                            asChild: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"/reports\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bell_BookOpen_Calendar_Clock_Cloud_CloudRain_FileText_MapPin_MessageSquare_QrCode_RefreshCw_Search_Sun_TrendingUp_UserCheck_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                        className: \"h-6 w-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                        lineNumber: 421,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"View Today's Reports\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                        lineNumber: 422,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                lineNumber: 420,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                            lineNumber: 414,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            variant: \"outline\",\n                            size: \"lg\",\n                            className: \"h-20 flex-col gap-2\",\n                            disabled: true,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bell_BookOpen_Calendar_Clock_Cloud_CloudRain_FileText_MapPin_MessageSquare_QrCode_RefreshCw_Search_Sun_TrendingUp_UserCheck_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    className: \"h-6 w-6\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                    lineNumber: 432,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Generate SF2 Form\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                    lineNumber: 433,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                            lineNumber: 426,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            variant: \"outline\",\n                            size: \"lg\",\n                            className: \"h-20 flex-col gap-2\",\n                            disabled: true,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bell_BookOpen_Calendar_Clock_Cloud_CloudRain_FileText_MapPin_MessageSquare_QrCode_RefreshCw_Search_Sun_TrendingUp_UserCheck_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                    className: \"h-6 w-6\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                    lineNumber: 442,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Send SMS Notifications\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                    lineNumber: 443,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                            lineNumber: 436,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                    lineNumber: 402,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid gap-6 lg:grid-cols-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"lg:col-span-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bell_BookOpen_Calendar_Clock_Cloud_CloudRain_FileText_MapPin_MessageSquare_QrCode_RefreshCw_Search_Sun_TrendingUp_UserCheck_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                    lineNumber: 453,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Recent Activity\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                            lineNumber: 452,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                            children: \"Latest attendance scans and student check-ins\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                            lineNumber: 456,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                    lineNumber: 451,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4 max-h-96 overflow-y-auto\",\n                                        children: mockRecentActivity.map((activity)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between p-4 rounded-lg border bg-card\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-10 h-10 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bell_BookOpen_Calendar_Clock_Cloud_CloudRain_FileText_MapPin_MessageSquare_QrCode_RefreshCw_Search_Sun_TrendingUp_UserCheck_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"h-5 w-5 text-blue-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 466,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                                lineNumber: 465,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-medium\",\n                                                                        children: activity.studentName\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 469,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-muted-foreground\",\n                                                                        children: [\n                                                                            activity.studentId,\n                                                                            \" • \",\n                                                                            activity.course\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 470,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    activity.location && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-muted-foreground flex items-center gap-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bell_BookOpen_Calendar_Clock_Cloud_CloudRain_FileText_MapPin_MessageSquare_QrCode_RefreshCw_Search_Sun_TrendingUp_UserCheck_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                                className: \"h-3 w-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 475,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            activity.location\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 474,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                                lineNumber: 468,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                        lineNumber: 464,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-right\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                className: getActionBadgeColor(activity.action),\n                                                                children: activity.action.replace(\"-\", \" \")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                                lineNumber: 482,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-muted-foreground mt-1\",\n                                                                children: activity.time\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                                lineNumber: 485,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                        lineNumber: 481,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, activity.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                lineNumber: 463,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                        lineNumber: 461,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                    lineNumber: 460,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                            lineNumber: 450,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bell_BookOpen_Calendar_Clock_Cloud_CloudRain_FileText_MapPin_MessageSquare_QrCode_RefreshCw_Search_Sun_TrendingUp_UserCheck_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                    lineNumber: 499,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"System Notifications\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                            lineNumber: 498,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                            children: \"Important alerts and updates\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                            lineNumber: 502,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                    lineNumber: 497,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3 max-h-96 overflow-y-auto\",\n                                        children: mockNotifications.map((notification)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_7__.Alert, {\n                                                className: notification.type === \"warning\" ? \"border-yellow-200 bg-yellow-50 dark:border-yellow-800 dark:bg-yellow-950\" : notification.type === \"error\" ? \"border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-950\" : notification.type === \"success\" ? \"border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950\" : \"border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-950\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bell_BookOpen_Calendar_Clock_Cloud_CloudRain_FileText_MapPin_MessageSquare_QrCode_RefreshCw_Search_Sun_TrendingUp_UserCheck_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                        lineNumber: 515,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_7__.AlertTitle, {\n                                                        className: \"text-sm\",\n                                                        children: notification.title\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                        lineNumber: 516,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_7__.AlertDescription, {\n                                                        className: \"text-xs\",\n                                                        children: [\n                                                            notification.message,\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-muted-foreground mt-1\",\n                                                                children: notification.timestamp\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                                lineNumber: 519,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                        lineNumber: 517,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, notification.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                lineNumber: 509,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                        lineNumber: 507,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                    lineNumber: 506,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                            lineNumber: 496,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                    lineNumber: 448,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid gap-6 lg:grid-cols-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-950\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"flex items-center gap-2 text-red-800 dark:text-red-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bell_BookOpen_Calendar_Clock_Cloud_CloudRain_FileText_MapPin_MessageSquare_QrCode_RefreshCw_Search_Sun_TrendingUp_UserCheck_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                    lineNumber: 536,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"At-Risk Students\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                            lineNumber: 535,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                            className: \"text-red-600 dark:text-red-300\",\n                                            children: \"Students requiring immediate attention\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                            lineNumber: 539,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                    lineNumber: 534,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: mockAtRiskStudents.map((student)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-4 rounded-lg bg-white dark:bg-gray-900 border border-red-200 dark:border-red-800\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between mb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-medium\",\n                                                                        children: student.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 549,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-muted-foreground\",\n                                                                        children: [\n                                                                            student.studentId,\n                                                                            \" • \",\n                                                                            student.course\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 550,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                                lineNumber: 548,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                className: getRiskLevelColor(student.riskLevel),\n                                                                children: [\n                                                                    student.riskLevel,\n                                                                    \" risk\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                                lineNumber: 554,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                        lineNumber: 547,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-2 gap-4 text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-muted-foreground\",\n                                                                        children: \"Attendance Rate\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 560,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-medium\",\n                                                                        children: [\n                                                                            student.attendanceRate,\n                                                                            \"%\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 561,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                                lineNumber: 559,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-muted-foreground\",\n                                                                        children: \"Consecutive Absences\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 564,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-medium\",\n                                                                        children: [\n                                                                            student.consecutiveAbsences,\n                                                                            \" days\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 565,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                                lineNumber: 563,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                        lineNumber: 558,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-muted-foreground mt-2\",\n                                                        children: [\n                                                            \"Last attendance: \",\n                                                            student.lastAttendance\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                        lineNumber: 568,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, student.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                lineNumber: 546,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                        lineNumber: 544,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                    lineNumber: 543,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                            lineNumber: 533,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bell_BookOpen_Calendar_Clock_Cloud_CloudRain_FileText_MapPin_MessageSquare_QrCode_RefreshCw_Search_Sun_TrendingUp_UserCheck_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                    lineNumber: 581,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Upcoming Classes\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                            lineNumber: 580,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                            children: \"Today's scheduled classes and expected attendance\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                            lineNumber: 584,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                    lineNumber: 579,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: mockUpcomingClasses.map((classItem)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-4 rounded-lg border bg-card\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between mb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-medium\",\n                                                                        children: classItem.subject\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 594,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-muted-foreground\",\n                                                                        children: classItem.instructor\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 595,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                                lineNumber: 593,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-right\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-medium\",\n                                                                        children: classItem.time\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 600,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-muted-foreground\",\n                                                                        children: classItem.room\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 601,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                                lineNumber: 599,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                        lineNumber: 592,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                variant: \"outline\",\n                                                                children: classItem.course\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                                lineNumber: 607,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-muted-foreground\",\n                                                                children: [\n                                                                    \"Expected: \",\n                                                                    classItem.expectedStudents,\n                                                                    \" students\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                                lineNumber: 608,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                        lineNumber: 606,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, classItem.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                lineNumber: 591,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                        lineNumber: 589,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                    lineNumber: 588,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                            lineNumber: 578,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                    lineNumber: 531,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bell_BookOpen_Calendar_Clock_Cloud_CloudRain_FileText_MapPin_MessageSquare_QrCode_RefreshCw_Search_Sun_TrendingUp_UserCheck_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                            lineNumber: 623,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Attendance Trends\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                    lineNumber: 622,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                    children: \"Weekly attendance patterns and trends\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                    lineNumber: 626,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                            lineNumber: 621,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-80\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_29__.ResponsiveContainer, {\n                                    width: \"100%\",\n                                    height: \"100%\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_30__.LineChart, {\n                                        data: mockTrendData,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_31__.CartesianGrid, {\n                                                strokeDasharray: \"3 3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                lineNumber: 634,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_32__.XAxis, {\n                                                dataKey: \"date\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                lineNumber: 635,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_33__.YAxis, {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                lineNumber: 636,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_34__.Tooltip, {\n                                                content: (param)=>{\n                                                    let { active, payload, label } = param;\n                                                    if (active && payload && payload.length) {\n                                                        var _payload_, _payload_1, _payload_2, _payload_3;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-white dark:bg-gray-800 p-3 border rounded-lg shadow-lg\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium\",\n                                                                    children: label\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 642,\n                                                                    columnNumber: 29\n                                                                }, void 0),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-green-600\",\n                                                                    children: [\n                                                                        \"Present: \",\n                                                                        (_payload_ = payload[0]) === null || _payload_ === void 0 ? void 0 : _payload_.value\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 643,\n                                                                    columnNumber: 29\n                                                                }, void 0),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-red-600\",\n                                                                    children: [\n                                                                        \"Absent: \",\n                                                                        (_payload_1 = payload[1]) === null || _payload_1 === void 0 ? void 0 : _payload_1.value\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 646,\n                                                                    columnNumber: 29\n                                                                }, void 0),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-yellow-600\",\n                                                                    children: [\n                                                                        \"Late: \",\n                                                                        (_payload_2 = payload[2]) === null || _payload_2 === void 0 ? void 0 : _payload_2.value\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 649,\n                                                                    columnNumber: 29\n                                                                }, void 0),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-blue-600\",\n                                                                    children: [\n                                                                        \"Rate: \",\n                                                                        (_payload_3 = payload[3]) === null || _payload_3 === void 0 ? void 0 : _payload_3.value,\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 652,\n                                                                    columnNumber: 29\n                                                                }, void 0)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                            lineNumber: 641,\n                                                            columnNumber: 27\n                                                        }, void 0);\n                                                    }\n                                                    return null;\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                lineNumber: 637,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_35__.Line, {\n                                                type: \"monotone\",\n                                                dataKey: \"present\",\n                                                stroke: \"#10b981\",\n                                                strokeWidth: 2,\n                                                name: \"Present\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                lineNumber: 661,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_35__.Line, {\n                                                type: \"monotone\",\n                                                dataKey: \"absent\",\n                                                stroke: \"#ef4444\",\n                                                strokeWidth: 2,\n                                                name: \"Absent\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                lineNumber: 668,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_35__.Line, {\n                                                type: \"monotone\",\n                                                dataKey: \"late\",\n                                                stroke: \"#f59e0b\",\n                                                strokeWidth: 2,\n                                                name: \"Late\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                lineNumber: 675,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_35__.Line, {\n                                                type: \"monotone\",\n                                                dataKey: \"attendanceRate\",\n                                                stroke: \"#3b82f6\",\n                                                strokeWidth: 3,\n                                                name: \"Attendance Rate\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                                lineNumber: 682,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                        lineNumber: 633,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                    lineNumber: 632,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                                lineNumber: 631,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                            lineNumber: 630,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n                    lineNumber: 620,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n            lineNumber: 291,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\page.tsx\",\n        lineNumber: 290,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardPage, \"YfzYF5/v9NTOL2IKwUnIUmf8H/Y=\");\n_c = DashboardPage;\nvar _c;\n$RefreshReg$(_c, \"DashboardPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});