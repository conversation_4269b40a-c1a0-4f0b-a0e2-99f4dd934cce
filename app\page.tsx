"use client"

import { useState, useEffect } from "react"
import { MainLayout } from "@/components/layout/main-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Separator } from "@/components/ui/separator"
import { Input } from "@/components/ui/input"
import {
  Users,
  UserCheck,
  UserX,
  Clock,
  TrendingUp,
  Calendar,
  QrCode,
  FileText,
  MessageSquare,
  Search,
  AlertTriangle,
  Bell,
  Cloud,
  Sun,
  CloudRain,
  MapPin,
  BookOpen,
  Activity,
  Zap,
  RefreshCw
} from "lucide-react"
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts'
import type {
  DashboardStats,
  WeatherData,
  RecentActivity,
  AtRiskStudent,
  UpcomingClass,
  SystemNotification,
  AttendanceTrendData
} from "@/types"

// Mock data for the dashboard
const mockStats: DashboardStats = {
  totalStudents: 1234,
  presentToday: 1089,
  absentToday: 145,
  lateToday: 23,
  attendanceRate: 88.2,
  totalCourses: 12,
  activeSections: 45
}

const mockWeather: WeatherData = {
  temperature: 28,
  condition: "Partly Cloudy",
  humidity: 65,
  icon: "partly-cloudy"
}

const mockRecentActivity: RecentActivity[] = [
  {
    id: "1",
    studentName: "Juan Dela Cruz",
    studentId: "2021-001234",
    action: "check-in",
    time: "8:15 AM",
    course: "BSIT 3A",
    location: "Main Building"
  },
  {
    id: "2",
    studentName: "Maria Santos",
    studentId: "2021-001235",
    action: "check-in",
    time: "8:12 AM",
    course: "BSBA 2B",
    location: "Business Building"
  },
  {
    id: "3",
    studentName: "Pedro Garcia",
    studentId: "2021-001236",
    action: "late-arrival",
    time: "8:35 AM",
    course: "BSIT 4A",
    location: "IT Building"
  },
  {
    id: "4",
    studentName: "Ana Rodriguez",
    studentId: "2021-001237",
    action: "check-in",
    time: "8:08 AM",
    course: "BSBA 1A",
    location: "Main Building"
  },
  {
    id: "5",
    studentName: "Carlos Mendoza",
    studentId: "2021-001238",
    action: "check-out",
    time: "5:30 PM",
    course: "BSIT 2B",
    location: "IT Building"
  }
]

const mockAtRiskStudents: AtRiskStudent[] = [
  {
    id: "1",
    name: "Roberto Silva",
    studentId: "2021-001240",
    course: "BSIT 1A",
    attendanceRate: 45,
    consecutiveAbsences: 5,
    lastAttendance: "3 days ago",
    riskLevel: "high"
  },
  {
    id: "2",
    name: "Lisa Chen",
    studentId: "2021-001241",
    course: "BSBA 3B",
    attendanceRate: 62,
    consecutiveAbsences: 3,
    lastAttendance: "2 days ago",
    riskLevel: "medium"
  },
  {
    id: "3",
    name: "Mark Johnson",
    studentId: "2021-001242",
    course: "BSIT 2A",
    attendanceRate: 71,
    consecutiveAbsences: 2,
    lastAttendance: "1 day ago",
    riskLevel: "low"
  }
]

const mockUpcomingClasses: UpcomingClass[] = [
  {
    id: "1",
    subject: "Database Systems",
    instructor: "Prof. Martinez",
    time: "9:00 AM",
    room: "IT-201",
    course: "BSIT 3A",
    expectedStudents: 35
  },
  {
    id: "2",
    subject: "Business Ethics",
    instructor: "Dr. Reyes",
    time: "10:30 AM",
    room: "BUS-105",
    course: "BSBA 2B",
    expectedStudents: 42
  },
  {
    id: "3",
    subject: "Web Development",
    instructor: "Engr. Santos",
    time: "1:00 PM",
    room: "IT-301",
    course: "BSIT 4A",
    expectedStudents: 28
  }
]

const mockNotifications: SystemNotification[] = [
  {
    id: "1",
    type: "warning",
    title: "Low Attendance Alert",
    message: "BSIT 1A has 65% attendance rate today",
    timestamp: "10 minutes ago",
    isRead: false,
    actionRequired: true
  },
  {
    id: "2",
    type: "info",
    title: "System Update",
    message: "QR scanner firmware updated successfully",
    timestamp: "1 hour ago",
    isRead: false
  },
  {
    id: "3",
    type: "success",
    title: "Report Generated",
    message: "Weekly attendance report is ready",
    timestamp: "2 hours ago",
    isRead: true
  }
]

const mockTrendData: AttendanceTrendData[] = [
  { date: "Mon", present: 1150, absent: 84, late: 15, attendanceRate: 92.1 },
  { date: "Tue", present: 1089, absent: 145, late: 23, attendanceRate: 88.2 },
  { date: "Wed", present: 1200, absent: 34, late: 8, attendanceRate: 96.8 },
  { date: "Thu", present: 1156, absent: 78, late: 19, attendanceRate: 93.7 },
  { date: "Fri", present: 1098, absent: 136, late: 28, attendanceRate: 89.0 },
  { date: "Sat", present: 856, absent: 378, late: 12, attendanceRate: 69.4 },
  { date: "Sun", present: 234, absent: 1000, late: 3, attendanceRate: 19.0 }
]

export default function DashboardPage() {
  const [currentTime, setCurrentTime] = useState(new Date())
  const [searchQuery, setSearchQuery] = useState("")
  const [isLoading, setIsLoading] = useState(false)

  // Update time every minute
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date())
    }, 60000)
    return () => clearInterval(timer)
  }, [])

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    })
  }

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const getWeatherIcon = (condition: string) => {
    switch (condition.toLowerCase()) {
      case 'sunny':
        return <Sun className="h-5 w-5" />
      case 'partly cloudy':
        return <Cloud className="h-5 w-5" />
      case 'rainy':
        return <CloudRain className="h-5 w-5" />
      default:
        return <Sun className="h-5 w-5" />
    }
  }

  const getActionBadgeColor = (action: string) => {
    switch (action) {
      case 'check-in':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
      case 'check-out':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
      case 'late-arrival':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
    }
  }

  const getRiskLevelColor = (level: string) => {
    switch (level) {
      case 'high':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
      case 'low':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
    }
  }

  const handleRefresh = () => {
    setIsLoading(true)
    // Simulate data refresh
    setTimeout(() => {
      setIsLoading(false)
    }, 1000)
  }

  return (
    <MainLayout title="Dashboard">
      <div className="space-y-6">
        {/* Welcome Header with Date, Time, and Weather */}
        <div className="education-gradient rounded-lg p-6 text-white">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            <div>
              <h1 className="text-3xl font-bold mb-2">
                Welcome to TSAT Attendance System
              </h1>
              <p className="text-blue-100 text-lg">
                {formatDate(currentTime)}
              </p>
            </div>
            <div className="flex items-center gap-6">
              <div className="text-right">
                <div className="text-2xl font-bold">{formatTime(currentTime)}</div>
                <div className="text-blue-100 text-sm">Current Time</div>
              </div>
              <Separator orientation="vertical" className="h-12 bg-blue-300" />
              <div className="flex items-center gap-3">
                <div className="text-white">
                  {getWeatherIcon(mockWeather.condition)}
                </div>
                <div>
                  <div className="text-xl font-semibold">{mockWeather.temperature}°C</div>
                  <div className="text-blue-100 text-sm">{mockWeather.condition}</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Student Search */}
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center gap-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search students by name or ID..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
              <Button disabled>
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Key Metrics Cards */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Attendance</CardTitle>
              <Users className="h-4 w-4 text-blue-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{mockStats.totalStudents.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">Active students</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Present Today</CardTitle>
              <UserCheck className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{mockStats.presentToday.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">Students checked in</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Absent Today</CardTitle>
              <UserX className="h-4 w-4 text-red-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">{mockStats.absentToday}</div>
              <p className="text-xs text-muted-foreground">Students absent</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Late Arrivals</CardTitle>
              <Clock className="h-4 w-4 text-yellow-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-yellow-600">{mockStats.lateToday}</div>
              <p className="text-xs text-muted-foreground">Late students</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Attendance Rate</CardTitle>
              <TrendingUp className="h-4 w-4 text-blue-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">{mockStats.attendanceRate}%</div>
              <Progress value={mockStats.attendanceRate} className="mt-2" />
            </CardContent>
          </Card>
        </div>

        {/* Quick Action Buttons */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Button
            size="lg"
            className="h-20 flex-col gap-2 bg-blue-600 hover:bg-blue-700"
            asChild
          >
            <a href="/scan">
              <QrCode className="h-8 w-8" />
              <span className="text-lg font-semibold">Scan QR Code</span>
            </a>
          </Button>

          <Button
            variant="outline"
            size="lg"
            className="h-20 flex-col gap-2"
            asChild
          >
            <a href="/reports">
              <FileText className="h-6 w-6" />
              <span>View Today's Reports</span>
            </a>
          </Button>

          <Button
            variant="outline"
            size="lg"
            className="h-20 flex-col gap-2"
            disabled
          >
            <BookOpen className="h-6 w-6" />
            <span>Generate SF2 Form</span>
          </Button>

          <Button
            variant="outline"
            size="lg"
            className="h-20 flex-col gap-2"
            disabled
          >
            <MessageSquare className="h-6 w-6" />
            <span>Send SMS Notifications</span>
          </Button>
        </div>

        {/* Main Content Grid */}
        <div className="grid gap-6 lg:grid-cols-3">
          {/* Recent Activity Feed */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                Recent Activity
              </CardTitle>
              <CardDescription>
                Latest attendance scans and student check-ins
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4 max-h-96 overflow-y-auto">
                {mockRecentActivity.map((activity) => (
                  <div key={activity.id} className="flex items-center justify-between p-4 rounded-lg border bg-card">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
                        <Users className="h-5 w-5 text-blue-600" />
                      </div>
                      <div>
                        <p className="font-medium">{activity.studentName}</p>
                        <p className="text-sm text-muted-foreground">
                          {activity.studentId} • {activity.course}
                        </p>
                        {activity.location && (
                          <p className="text-xs text-muted-foreground flex items-center gap-1">
                            <MapPin className="h-3 w-3" />
                            {activity.location}
                          </p>
                        )}
                      </div>
                    </div>
                    <div className="text-right">
                      <Badge className={getActionBadgeColor(activity.action)}>
                        {activity.action.replace('-', ' ')}
                      </Badge>
                      <p className="text-xs text-muted-foreground mt-1">
                        {activity.time}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* System Notifications */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Bell className="h-5 w-5" />
                System Notifications
              </CardTitle>
              <CardDescription>
                Important alerts and updates
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3 max-h-96 overflow-y-auto">
                {mockNotifications.map((notification) => (
                  <Alert key={notification.id} className={
                    notification.type === 'warning' ? 'border-yellow-200 bg-yellow-50 dark:border-yellow-800 dark:bg-yellow-950' :
                    notification.type === 'error' ? 'border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-950' :
                    notification.type === 'success' ? 'border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950' :
                    'border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-950'
                  }>
                    <AlertTriangle className="h-4 w-4" />
                    <AlertTitle className="text-sm">{notification.title}</AlertTitle>
                    <AlertDescription className="text-xs">
                      {notification.message}
                      <div className="text-xs text-muted-foreground mt-1">
                        {notification.timestamp}
                      </div>
                    </AlertDescription>
                  </Alert>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* At-Risk Students and Upcoming Classes */}
        <div className="grid gap-6 lg:grid-cols-2">
          {/* At-Risk Students Alert Panel */}
          <Card className="border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-950">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-red-800 dark:text-red-200">
                <AlertTriangle className="h-5 w-5" />
                At-Risk Students
              </CardTitle>
              <CardDescription className="text-red-600 dark:text-red-300">
                Students requiring immediate attention
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {mockAtRiskStudents.map((student) => (
                  <div key={student.id} className="p-4 rounded-lg bg-white dark:bg-gray-900 border border-red-200 dark:border-red-800">
                    <div className="flex items-center justify-between mb-2">
                      <div>
                        <p className="font-medium">{student.name}</p>
                        <p className="text-sm text-muted-foreground">
                          {student.studentId} • {student.course}
                        </p>
                      </div>
                      <Badge className={getRiskLevelColor(student.riskLevel)}>
                        {student.riskLevel} risk
                      </Badge>
                    </div>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <p className="text-muted-foreground">Attendance Rate</p>
                        <p className="font-medium">{student.attendanceRate}%</p>
                      </div>
                      <div>
                        <p className="text-muted-foreground">Consecutive Absences</p>
                        <p className="font-medium">{student.consecutiveAbsences} days</p>
                      </div>
                    </div>
                    <p className="text-xs text-muted-foreground mt-2">
                      Last attendance: {student.lastAttendance}
                    </p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Upcoming Classes Schedule */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                Upcoming Classes
              </CardTitle>
              <CardDescription>
                Today's scheduled classes and expected attendance
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {mockUpcomingClasses.map((classItem) => (
                  <div key={classItem.id} className="p-4 rounded-lg border bg-card">
                    <div className="flex items-center justify-between mb-2">
                      <div>
                        <p className="font-medium">{classItem.subject}</p>
                        <p className="text-sm text-muted-foreground">
                          {classItem.instructor}
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="font-medium">{classItem.time}</p>
                        <p className="text-sm text-muted-foreground">
                          {classItem.room}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <Badge variant="outline">{classItem.course}</Badge>
                      <p className="text-sm text-muted-foreground">
                        Expected: {classItem.expectedStudents} students
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Attendance Trends Chart */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              Attendance Trends
            </CardTitle>
            <CardDescription>
              Weekly attendance patterns and trends
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={mockTrendData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis />
                  <Tooltip
                    content={({ active, payload, label }) => {
                      if (active && payload && payload.length) {
                        return (
                          <div className="bg-white dark:bg-gray-800 p-3 border rounded-lg shadow-lg">
                            <p className="font-medium">{label}</p>
                            <p className="text-green-600">
                              Present: {payload[0]?.value}
                            </p>
                            <p className="text-red-600">
                              Absent: {payload[1]?.value}
                            </p>
                            <p className="text-yellow-600">
                              Late: {payload[2]?.value}
                            </p>
                            <p className="text-blue-600">
                              Rate: {payload[3]?.value}%
                            </p>
                          </div>
                        )
                      }
                      return null
                    }}
                  />
                  <Line
                    type="monotone"
                    dataKey="present"
                    stroke="#10b981"
                    strokeWidth={2}
                    name="Present"
                  />
                  <Line
                    type="monotone"
                    dataKey="absent"
                    stroke="#ef4444"
                    strokeWidth={2}
                    name="Absent"
                  />
                  <Line
                    type="monotone"
                    dataKey="late"
                    stroke="#f59e0b"
                    strokeWidth={2}
                    name="Late"
                  />
                  <Line
                    type="monotone"
                    dataKey="attendanceRate"
                    stroke="#3b82f6"
                    strokeWidth={3}
                    name="Attendance Rate"
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  )
}
